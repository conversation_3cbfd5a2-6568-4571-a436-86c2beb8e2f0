{"dashboard": {"id": null, "title": "Appointment Metrics Dashboard", "tags": ["appointments", "metrics"], "timezone": "", "panels": [{"id": 1, "title": "Total Scheduled Appointments Successfully", "type": "stat", "targets": [{"expr": "count(count by (appointment_id) (appointment_scheduled_success_total[$__range]))", "legendFormat": "Successfully Scheduled", "refId": "A", "datasource": {"type": "prometheus", "uid": "prometheus"}}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"steps": [{"color": "green", "value": null}]}, "unit": "short"}}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 0}}, {"id": 2, "title": "Total Scheduling Failures", "type": "stat", "targets": [{"expr": "count(count by (appointment_id) (appointment_scheduled_conflict_total[$__range]))", "legendFormat": "Conflicts", "refId": "A", "datasource": {"type": "prometheus", "uid": "prometheus"}}, {"expr": "count(count by (appointment_id) (appointment_scheduled_error_total[$__range]))", "legendFormat": "Internal Errors", "refId": "B", "datasource": {"type": "prometheus", "uid": "prometheus"}}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"steps": [{"color": "red", "value": null}]}, "unit": "short"}}, "gridPos": {"h": 8, "w": 6, "x": 6, "y": 0}}, {"id": 3, "title": "Appointments Completed Successfully", "type": "stat", "targets": [{"expr": "count(count by (appointment_id) (appointment_completed_success_total[$__range]))", "legendFormat": "Completed", "refId": "A", "datasource": {"type": "prometheus", "uid": "prometheus"}}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"steps": [{"color": "blue", "value": null}]}, "unit": "short"}}, "gridPos": {"h": 8, "w": 6, "x": 12, "y": 0}}, {"id": 4, "title": "Appointments Rescheduled", "type": "stat", "targets": [{"expr": "count(count by (appointment_id) (appointment_rescheduled_success_total[$__range]))", "legendFormat": "Rescheduled", "refId": "A", "datasource": {"type": "prometheus", "uid": "prometheus"}}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"steps": [{"color": "orange", "value": null}]}, "unit": "short"}}, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 0}}, {"id": 5, "title": "Scheduling Success vs Failures Over Time", "type": "timeseries", "targets": [{"expr": "rate(appointment_scheduled_success_total[$__rate_interval])", "legendFormat": "Successful Scheduling Rate", "refId": "A", "datasource": {"type": "prometheus", "uid": "prometheus"}}, {"expr": "rate(appointment_scheduled_conflict_total[$__rate_interval])", "legendFormat": "Conflict Rate", "refId": "B", "datasource": {"type": "prometheus", "uid": "prometheus"}}, {"expr": "rate(appointment_scheduled_error_total[$__rate_interval])", "legendFormat": "Error Rate", "refId": "C", "datasource": {"type": "prometheus", "uid": "prometheus"}}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"steps": [{"color": "green", "value": null}]}, "unit": "reqps"}}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 8}}, {"id": 6, "title": "Method Execution Times (Scheduling Process)", "type": "table", "targets": [{"expr": "avg by (method) (rate(appointment_method_execution_seconds_sum[$__rate_interval]) / rate(appointment_method_execution_seconds_count[$__rate_interval])) * 1000", "legendFormat": "", "refId": "A", "format": "table", "datasource": {"type": "prometheus", "uid": "prometheus"}}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto"}, "mappings": [], "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 100}, {"color": "red", "value": 500}]}, "unit": "ms"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true}, "indexByName": {}, "renameByName": {"method": "Method Name", "Value": "Average Duration (ms)"}}}]}, {"id": 7, "title": "Appointment Outcomes by Salon", "type": "timeseries", "targets": [{"expr": "rate(appointment_scheduled_success_total[$__rate_interval])", "legendFormat": "Scheduled - Salon {{salon_id}}", "refId": "A", "datasource": {"type": "prometheus", "uid": "prometheus"}}, {"expr": "rate(appointment_completed_success_total[$__rate_interval])", "legendFormat": "Completed - Salon {{salon_id}}", "refId": "B", "datasource": {"type": "prometheus", "uid": "prometheus"}}, {"expr": "rate(appointment_rescheduled_success_total[$__rate_interval])", "legendFormat": "Rescheduled - Salon {{salon_id}}", "refId": "C", "datasource": {"type": "prometheus", "uid": "prometheus"}}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"steps": [{"color": "green", "value": null}]}, "unit": "reqps"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}}], "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "templating": {"list": []}, "annotations": {"list": []}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "links": [], "liveNow": false, "refresh": "10s", "schemaVersion": 37, "style": "dark", "uid": "appointment-metrics", "version": 1, "weekStart": ""}}