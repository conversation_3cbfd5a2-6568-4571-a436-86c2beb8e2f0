global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  # Scrape your Animalia application running on the host
  - job_name: 'animalia-backend'
    static_configs:
      - targets: ['host.docker.internal:8081']
    metrics_path: '/api/actuator/prometheus'
    scrape_interval: 5s
    scrape_timeout: 5s
    honor_labels: true
    params:
      format: ['prometheus']

  # Scrape Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 15s

  # Scrape Grafana metrics (optional)
  - job_name: 'grafana'
    static_configs:
      - targets: ['grafana:3000']
    scrape_interval: 15s
